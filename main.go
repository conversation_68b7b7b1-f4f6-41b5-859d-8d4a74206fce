package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"sync"
	"time"

	v2raycore "github.com/v2fly/v2ray-core/v5"
	"github.com/v2fly/v2ray-core/v5/infra/conf/json"
	"gopkg.in/yaml.v3"
)

const (
	subscriptionURL = "https://blue-cell-1adc.teleapp.top/fc1561f8-7d8c-4f81-bc9c-66cfb3aa7a5f?clash"
	socksPort       = 1080
	apiPort         = "8080"
)

var (
	proxies         []map[string]interface{}
	proxyNames      []string
	currentOutbound string // "" for random, otherwise specific name
	currentInstance *v2raycore.Instance
	mu              sync.Mutex
)

func fetchAndLoadConfig() error {
	log.Println("[fetchAndLoadConfig] 开始加载配置...")
	const cacheFile = "config_cache.yaml"
	const cacheDuration = 24 * time.Hour

	useCache := false
	if fi, err := os.Stat(cacheFile); err == nil {
		if time.Since(fi.ModTime()) < cacheDuration {
			useCache = true
		}
	}

	var body []byte
	var err error
	if useCache {
		log.Println("[fetchAndLoadConfig] 使用本地缓存...")
		body, err = os.ReadFile(cacheFile)
		if err != nil {
			return fmt.Errorf("读取缓存失败: %v", err)
		}
		log.Printf("使用本地缓存的配置文件: %s", cacheFile)
	} else {
		log.Println("[fetchAndLoadConfig] 正在下载远程配置...")
		resp, err := http.Get(subscriptionURL)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		body, err = io.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		// 写入缓存
		_ = os.WriteFile(cacheFile, body, 0644)
		log.Printf("已下载并缓存配置文件: %s", cacheFile)
	}

	log.Println("[fetchAndLoadConfig] 解析YAML配置...")
	var clashConfig map[string]interface{}
	if err := yaml.Unmarshal(body, &clashConfig); err != nil {
		return err
	}

	proxiesAny, ok := clashConfig["proxies"].([]interface{})
	if !ok {
		return fmt.Errorf("no proxies found in config")
	}

	proxies = nil
	proxyNames = nil
	for _, pa := range proxiesAny {
		pm, ok := pa.(map[string]interface{})
		if !ok {
			continue
		}
		typ, _ := pm["type"].(string)
		if typ != "vmess" && typ != "vless" {
			continue // only support vmess and vless
		}
		name, _ := pm["name"].(string)
		if name == "" {
			continue
		}
		proxies = append(proxies, pm)
		proxyNames = append(proxyNames, name)
	}

	log.Printf("[fetchAndLoadConfig] 解析完成，已加载 %d 个代理", len(proxies))
	return nil
}

func convertToOutbound(p map[string]interface{}) map[string]interface{} {
	typ := p["type"].(string)
	server := p["server"].(string)
	// 修复port类型兼容
	var port int
	switch v := p["port"].(type) {
	case float64:
		port = int(v)
	case int:
		port = v
	default:
		panic(fmt.Sprintf("unsupported port type: %T", v))
	}
	uuid := p["uuid"].(string)
	network, _ := p["network"].(string)
	if network == "" {
		network = "tcp"
	}
	tls, _ := p["tls"].(bool)
	skipCert, _ := p["skip-cert-verify"].(bool)

	streamSettings := map[string]interface{}{
		"network": network,
	}
	if tls {
		streamSettings["security"] = "tls"
		streamSettings["tlsSettings"] = map[string]interface{}{
			"serverName":    server,
			"allowInsecure": skipCert,
		}
	}
	if network == "ws" {
		wsOpts, _ := p["ws-opts"].(map[string]interface{})
		path, _ := wsOpts["path"].(string)
		headersAny, _ := wsOpts["headers"].(map[string]interface{})
		headers := map[string]interface{}{}
		for k, v := range headersAny {
			headers[k] = v
		}
		streamSettings["wsSettings"] = map[string]interface{}{
			"path":    path,
			"headers": headers,
		}
	}

	var settings map[string]interface{}
	if typ == "vmess" {
		// 修复alterId类型兼容
		var alterId int
		switch v := p["alterId"].(type) {
		case float64:
			alterId = int(v)
		case int:
			alterId = v
		case nil:
			alterId = 0
		default:
			panic(fmt.Sprintf("unsupported alterId type: %T", v))
		}
		security, _ := p["cipher"].(string)
		if security == "" {
			security = "auto"
		}
		settings = map[string]interface{}{
			"vnext": []map[string]interface{}{
				{
					"address": server,
					"port":    port,
					"users": []map[string]interface{}{
						{
							"id":       uuid,
							"alterId":  alterId,
							"security": security,
						},
					},
				},
			},
		}
	} else if typ == "vless" {
		flow, _ := p["flow"].(string)
		encryption, _ := p["encryption"].(string)
		if encryption == "" {
			encryption = "none"
		}
		settings = map[string]interface{}{
			"vnext": []map[string]interface{}{
				{
					"address": server,
					"port":    port,
					"users": []map[string]interface{}{
						{
							"id":         uuid,
							"encryption": encryption,
							"flow":       flow,
						},
					},
				},
			},
		}
	}

	// 保证settings字段非nil
	if settings == nil {
		settings = map[string]interface{}{}
	}
	result := map[string]interface{}{
		"protocol": typ,
		"settings": settings,
		"tag":      p["name"].(string),
	}
	if typ == "vmess" || typ == "vless" {
		result["streamSettings"] = streamSettings
	}
	return result
}

func restartProxyServer() error {
	log.Println("[restartProxyServer] 正在重启代理服务...")
	mu.Lock()
	defer mu.Unlock()

	if currentInstance != nil {
		log.Println("[restartProxyServer] 关闭旧实例...")
		currentInstance.Close()
		currentInstance = nil
	}

	log.Println("[restartProxyServer] 构建inbounds配置...")
	inbounds := []map[string]interface{}{
		{
			"port":     socksPort,
			"listen":   "127.0.0.1",
			"protocol": "socks",
			"settings": map[string]interface{}{
				"auth": "noauth",
				"udp":  true,
			},
			"tag": "socks-in",
			"sniffing": map[string]interface{}{
				"enabled":      true,
				"destOverride": []string{"http", "tls"},
			},
		},
	}

	var outbounds []map[string]interface{}
	var outboundTags []string

	log.Println("[restartProxyServer] 构建outbounds配置...")
	if currentOutbound == "random" || currentOutbound == "" {
		currentOutbound = "random"
		for _, p := range proxies {
			ob := convertToOutbound(p)
			if ob == nil {
				continue
			}
			outbounds = append(outbounds, ob)
			outboundTags = append(outboundTags, ob["tag"].(string))
		}
	} else {
		var selectedProxy map[string]interface{}
		for _, p := range proxies {
			if p["name"].(string) == currentOutbound {
				selectedProxy = p
				break
			}
		}
		if selectedProxy == nil {
			return fmt.Errorf("proxy not found: %s", currentOutbound)
		}
		ob := convertToOutbound(selectedProxy)
		if ob == nil {
			return fmt.Errorf("failed to convert proxy: %s", currentOutbound)
		}
		ob["tag"] = "proxy"
		outbounds = []map[string]interface{}{ob}
	}

	log.Println("[restartProxyServer] 追加freedom直连出口...")
	outbounds = append(outbounds, map[string]interface{}{
		"protocol": "freedom",
		"settings": map[string]interface{}{},
		"tag":      "direct",
	})

	log.Println("[restartProxyServer] 构建总配置...")
	config := map[string]interface{}{
		"inbounds":  inbounds,
		"outbounds": outbounds,
	}

	if currentOutbound == "random" {
		config["routing"] = map[string]interface{}{
			"balancers": []map[string]interface{}{
				{
					"tag":      "bal",
					"selector": outboundTags,
					"strategy": map[string]interface{}{"type": "random"},
				},
			},
			"rules": []map[string]interface{}{
				{
					"type":        "field",
					"ip":          []string{"geoip:private"},
					"outboundTag": "direct",
				},
				{
					"type":        "field",
					"balancerTag": "bal",
				},
			},
		}
	} else {
		config["routing"] = map[string]interface{}{
			"rules": []map[string]interface{}{
				{
					"type":        "field",
					"ip":          []string{"geoip:private"},
					"outboundTag": "direct",
				},
				{
					"type":        "field",
					"outboundTag": "proxy",
				},
			},
		}
	}

	configBytes, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return err
	}
	log.Println("[restartProxyServer] 最终生成的Xray配置如下：")
	fmt.Println("==== Xray Config ====")
	// fmt.Println(string(configBytes))
	// 写入本地coreConfig.json
	if err := os.WriteFile("coreConfig.json", configBytes, 0644); err != nil {
		log.Printf("[restartProxyServer] 写入coreConfig.json失败: %v", err)
	} else {
		log.Println("[restartProxyServer] 已写入coreConfig.json")
	}

	log.Println("[restartProxyServer] 启动V2Ray实例...")
	instance, err := v2raycore.StartInstance("json", configBytes)
	if err != nil {
		log.Printf("[restartProxyServer] 启动失败: %v", err)
		return err
	}
	log.Println("[restartProxyServer] V2Ray实例启动成功")
	_ = instance // 如需后续操作可保留
	return nil
}

func handleProxies(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string][]string{"proxies": proxyNames})
}

func handleSelect(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		Name string `json:"name"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if req.Name != "random" {
		found := false
		for _, name := range proxyNames {
			if name == req.Name {
				found = true
				break
			}
		}
		if !found {
			http.Error(w, "Proxy not found", http.StatusNotFound)
			return
		}
	}

	currentOutbound = req.Name
	if err := restartProxyServer(); err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

func main() {
	log.Println("[main] 启动中...")
	if err := fetchAndLoadConfig(); err != nil {
		log.Fatalf("[main] 加载配置失败: %v", err)
	}

	// Initial start with random
	log.Println("[main] 启动代理服务（随机模式）...")
	if err := restartProxyServer(); err != nil {
		log.Fatalf("[main] 启动代理服务失败: %v", err)
	}

	http.HandleFunc("/proxies", handleProxies)
	http.HandleFunc("/select", handleSelect)

	log.Printf("[main] API服务启动于 :%s", apiPort)
	log.Fatal(http.ListenAndServe(":"+apiPort, nil))
}
