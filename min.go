package main

import (
	"log"

	v2raycore "github.com/v2fly/v2ray-core/v5"
	"github.com/v2fly/v2ray-core/v5/infra/conf/json"
)

func main() {
	config := []byte(`{
      "inbounds": [{
        "port": 10808,
        "listen": "127.0.0.1",
        "protocol": "socks",
        "settings": { "auth": "noauth", "udp": true }
      }],
      "outbounds": [{
        "protocol": "freedom",
        "settings": {}
      }]
    }`)
	_, err := v2raycore.StartInstance("json", config)
	if err != nil {
		log.Fatalf("启动失败: %v", err)
	}
	log.Println("启动成功")
	select {}
}
